{
  "actions": [
    {
      "action": "GET",
      "entity": "OBJECT",
      "location": {
        "space": "MY_SPACE",
        "project": "MY_PROJECT",
        "experiment": "MY_EXPERIMENT_01",
        "path": "/MY_SPACE/MY_PROJECT/MY_EXPERIMENT_01"
      },
      "properties": ["name", "description"],
      "attributes": {
        "parents": "TRUE",
        "children": "TRUE",
        "limit": 3
      },
      "payload": {
        "code": "",
        "type": "",
        "properties": {},
        "parents": [],
        "children": []
      }
    }
  ]
}

//path is compound of the rest of the location fields, the last one will be the required entity
//if any property appears, it should run also the get properties for that entity, if no property is there, just get the specified entity
//if parents and/or children appears, fetch these together with the entity
//limit indicates the total number of entities wanted to be fetched (last 3 for example)
//payload will be used just for creating. For the moment we are just working on retrieval